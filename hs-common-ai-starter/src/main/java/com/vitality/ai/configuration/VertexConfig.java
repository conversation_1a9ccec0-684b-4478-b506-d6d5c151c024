package com.vitality.ai.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.api.gax.grpc.InstantiatingGrpcChannelProvider;
import com.google.api.gax.rpc.TransportChannelProvider;
import com.google.auth.http.HttpTransportFactory;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.vertexai.Transport;
import com.google.cloud.vertexai.VertexAI;
import com.google.cloud.vertexai.api.GenerationConfig;
import com.google.cloud.vertexai.api.PredictionServiceClient;
import com.google.cloud.vertexai.api.PredictionServiceSettings;
import com.google.cloud.vertexai.generativeai.GenerativeModel;
import com.vitality.ai.properties.VertexProperties;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.DisabledChatModel;
import dev.langchain4j.model.vertexai.VertexAiGeminiChatModel;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;
import io.grpc.netty.shaded.io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.security.auth.x500.X500Principal;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;

@Configuration
@Profile("google")
@EnableConfigurationProperties(VertexProperties.class)
@RequiredArgsConstructor
@Slf4j
public class VertexConfig {

    private final VertexProperties vertexProperties;

    @Autowired
    private ObjectMapper objectMapper;

    @Bean
    public GoogleCredentials googleCredentials() throws IOException {
        final HttpTransportFactory httpTransportFactory = createHttpTransportFactory();

        final ServiceAccountCredentials credentials = createServiceAccountCredentials(httpTransportFactory);
        credentials.refreshIfExpired();
        return credentials;
    }

    @Bean
    public ChatModel vertexAiGeminiChatModel(GoogleCredentials googleCredentials) throws IOException {
        final SslContext insecureCtx = GrpcSslContexts.forClient()
            .trustManager(InsecureTrustManagerFactory.INSTANCE)
            .build();

        final TransportChannelProvider channelProvider =
            InstantiatingGrpcChannelProvider.newBuilder()
                .setEndpoint(vertexProperties.getLocation() + "-aiplatform.googleapis.com:443")
                .setChannelConfigurator(ch ->                     // customise the Netty builder
                    ((NettyChannelBuilder) ch).sslContext(insecureCtx))
                .build();


        final PredictionServiceSettings predSettings =
            PredictionServiceSettings.newBuilder()
                .setCredentialsProvider(FixedCredentialsProvider.create(googleCredentials))
                .setTransportChannelProvider(channelProvider)
                .build();


        final VertexAI vertexAI =
            new VertexAI.Builder()
                .setCredentials(googleCredentials)
                .setProjectId(vertexProperties.getProject())
                .setLocation(vertexProperties.getLocation())
                .setTransport(Transport.GRPC)
                .setPredictionClientSupplier(() -> {
                    try {
                        return PredictionServiceClient.create(predSettings);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                })
                .build();

        final GenerativeModel model = new GenerativeModel(vertexProperties.getModel(), vertexAI);

        final GenerationConfig generationConfigBuilder = GenerationConfig.newBuilder()
            .setTemperature(vertexProperties.getTemperature())
            .setMaxOutputTokens(vertexProperties.getMaxOutputTokens())
            .setTopK(vertexProperties.getTopK())
            .setTopP(vertexProperties.getTopP())
            .setSeed(vertexProperties.getSeed())
            .build();

        return new VertexAiGeminiChatModel(model,generationConfigBuilder);
    }

    private ServiceAccountCredentials createServiceAccountCredentials(final HttpTransportFactory httpTransportFactory) throws IOException {
        final ServiceAccountCredentials credentials;

        // Check legacy credential property first for backward compatibility
        if (vertexProperties.getCredentials().hasFileCredentials()) {
            // File-based authentication
            credentials = (ServiceAccountCredentials) ServiceAccountCredentials.fromStream(
                    new FileInputStream(vertexProperties.getCredentials().getCredentialFile()),
                httpTransportFactory
            ).createScoped("https://www.googleapis.com/auth/cloud-platform");
        } else if (vertexProperties.getCredentials().hasDirectCredentials()) {
            // Direct properties authentication
            final String credentialsStr = objectMapper.writeValueAsString(vertexProperties.getCredentials());

            credentials = (ServiceAccountCredentials) ServiceAccountCredentials.fromStream(
                new ByteArrayInputStream(credentialsStr.getBytes()),
                httpTransportFactory
            ).createScoped("https://www.googleapis.com/auth/cloud-platform");
        } else {
            throw new IllegalArgumentException("No valid authentication method configured. Please provide either 'credential' file path or individual credential properties under 'credentials'.");
        }
        return credentials;
    }

    private HttpTransportFactory createHttpTransportFactory() {
        return () -> {
            try {
                TrustManager[] trustManagers = new TrustManager[]{
                        new X509TrustManager() {

                            @Override
                            public X509Certificate[] getAcceptedIssuers() {
                                return new X509Certificate[0]; // Return empty array
                            }

                            @Override
                            public void checkClientTrusted(final X509Certificate[] certs, final String authType) {
                            }

                            @Override
                            public void checkServerTrusted(X509Certificate[] certs, String authType) throws CertificateException {
                                if (certs == null || certs.length == 0) {
                                    throw new CertificateException("Server certificate chain is empty.");
                                }

                                final X509Certificate serverCert = certs[0]; // The actual server certificate
                                final X500Principal serverCertIssuer = serverCert.getIssuerX500Principal();
                                final String serverCertIssuerDn = serverCertIssuer.getName();

                                final boolean isFromExpectedProxy = Arrays.stream(certs)
                                    .anyMatch(cert -> vertexProperties.getAllowedDns().contains(cert.getSubjectX500Principal().getName()));

                                if (isFromExpectedProxy) {
                                    log.info("Custom TrustManager: Accepting certificate issued by expected corporate proxy CA.");
                                } else {
                                    log.info("Custom TrustManager: Accepting certificate. WARNING: Not from an expected corporate proxy CA. Issuer: {}", serverCertIssuerDn);
                                }
                            }
                        }
                };

                final SSLContext sc = SSLContext.getInstance("TLS");
                sc.init(null, trustManagers, new java.security.SecureRandom());

                return new NetHttpTransport.Builder()
                        .setSslSocketFactory(sc.getSocketFactory())
                        .build();

            } catch (GeneralSecurityException e) {
                throw new RuntimeException("Error setting up custom SSLContext for HttpTransport", e);
            }
        };
    }

    @Profile("!google")
    @Bean
    public ChatModel disabledChatModel() {
        return new DisabledChatModel();
    }
}
