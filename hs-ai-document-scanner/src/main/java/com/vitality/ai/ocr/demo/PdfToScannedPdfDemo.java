package com.vitality.ai.ocr.demo;

import com.vitality.ai.ocr.service.PdfToScannedPdfService;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Demo class to test the PdfToScannedPdfService functionality.
 * This creates sample PDF files and converts them to "scanned" PDFs.
 */
public class PdfToScannedPdfDemo {

    private final PdfToScannedPdfService service;
    private final Path tempDir;

    public PdfToScannedPdfDemo() throws IOException {
        this.service = new PdfToScannedPdfService();
        this.tempDir = Files.createTempDirectory("pdf-demo");
        System.out.println("Demo working directory: " + tempDir.toAbsolutePath());
    }

    public static void main(String[] args) {
        System.out.println("🚀 Starting PDF to Scanned PDF Demo...\n");
        
        try {
            PdfToScannedPdfDemo demo = new PdfToScannedPdfDemo();
            demo.runAllDemos();
            System.out.println("\n✅ All demos completed successfully!");
            
        } catch (Exception e) {
            System.err.println("\n❌ Demo failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    public void runAllDemos() throws IOException {
        System.out.println("📄 Creating sample PDF documents...");
        
        // Demo 1: Basic conversion
        demoBasicConversion();
        
        // Demo 2: Custom DPI conversion
        demoCustomDpiConversion();
        
        // Demo 3: InputStream conversion
        demoInputStreamConversion();
        
        // Demo 4: Byte array conversion
        demoByteArrayConversion();
        
        // Demo 5: Different page sizes
        demoDifferentPageSizes();
        
        System.out.println("\n📁 Demo files saved in: " + tempDir.toAbsolutePath());
    }

    private void demoBasicConversion() throws IOException {
        System.out.println("\n1️⃣ Demo: Basic PDF to Scanned PDF Conversion");
        
        // Create a sample PDF
        File inputPdf = createSamplePdf("demo-input.pdf", "Sample Document", 
                "This is a sample PDF document with text content.", 
                "It will be converted to a scanned PDF format.");
        
        // Convert to scanned PDF
        long startTime = System.currentTimeMillis();
        byte[] scannedPdfBytes = service.convertToScannedPdf(inputPdf);
        long duration = System.currentTimeMillis() - startTime;
        
        // Save the result
        File outputPdf = tempDir.resolve("demo-output-scanned.pdf").toFile();
        Files.write(outputPdf.toPath(), scannedPdfBytes);
        
        // Verify the result
        try (PDDocument document = Loader.loadPDF(scannedPdfBytes)) {
            System.out.println("   ✓ Input file: " + inputPdf.getName() + " (" + inputPdf.length() + " bytes)");
            System.out.println("   ✓ Output file: " + outputPdf.getName() + " (" + scannedPdfBytes.length + " bytes)");
            System.out.println("   ✓ Pages: " + document.getNumberOfPages());
            System.out.println("   ✓ Conversion time: " + duration + "ms");
        }
    }

    private void demoCustomDpiConversion() throws IOException {
        System.out.println("\n2️⃣ Demo: Custom DPI Conversion");
        
        File inputPdf = createSamplePdf("demo-dpi-input.pdf", "DPI Test Document", 
                "This document will be converted at different DPI settings.", 
                "Higher DPI = better quality but larger file size.");
        
        int[] dpiSettings = {150, 300, 600};
        
        for (int dpi : dpiSettings) {
            long startTime = System.currentTimeMillis();
            byte[] scannedPdfBytes = service.convertToScannedPdf(inputPdf, dpi);
            long duration = System.currentTimeMillis() - startTime;
            
            File outputPdf = tempDir.resolve("demo-output-" + dpi + "dpi.pdf").toFile();
            Files.write(outputPdf.toPath(), scannedPdfBytes);
            
            System.out.println("   ✓ " + dpi + " DPI: " + outputPdf.getName() + 
                             " (" + scannedPdfBytes.length + " bytes, " + duration + "ms)");
        }
    }

    private void demoInputStreamConversion() throws IOException {
        System.out.println("\n3️⃣ Demo: InputStream Conversion");
        
        File inputPdf = createSamplePdf("demo-stream-input.pdf", "Stream Test", 
                "This PDF is being converted from an InputStream.", 
                "Useful for web services and API integrations.");
        
        try (FileInputStream inputStream = new FileInputStream(inputPdf)) {
            long startTime = System.currentTimeMillis();
            byte[] scannedPdfBytes = service.convertToScannedPdf(inputStream);
            long duration = System.currentTimeMillis() - startTime;
            
            File outputPdf = tempDir.resolve("demo-output-from-stream.pdf").toFile();
            Files.write(outputPdf.toPath(), scannedPdfBytes);
            
            System.out.println("   ✓ Converted from InputStream: " + outputPdf.getName() + 
                             " (" + scannedPdfBytes.length + " bytes, " + duration + "ms)");
        }
    }

    private void demoByteArrayConversion() throws IOException {
        System.out.println("\n4️⃣ Demo: Byte Array Conversion");
        
        File inputPdf = createSamplePdf("demo-bytes-input.pdf", "Byte Array Test", 
                "This PDF is being converted from a byte array.", 
                "Perfect for in-memory processing workflows.");
        
        byte[] inputBytes = Files.readAllBytes(inputPdf.toPath());
        
        long startTime = System.currentTimeMillis();
        byte[] scannedPdfBytes = service.convertToScannedPdf(inputBytes);
        long duration = System.currentTimeMillis() - startTime;
        
        File outputPdf = tempDir.resolve("demo-output-from-bytes.pdf").toFile();
        Files.write(outputPdf.toPath(), scannedPdfBytes);
        
        System.out.println("   ✓ Converted from byte array: " + outputPdf.getName() + 
                         " (" + scannedPdfBytes.length + " bytes, " + duration + "ms)");
    }

    private void demoDifferentPageSizes() throws IOException {
        System.out.println("\n5️⃣ Demo: Different Page Sizes");
        
        // Create PDFs with different page sizes
        PDRectangle[] pageSizes = {PDRectangle.A4, PDRectangle.LETTER, PDRectangle.A3};
        String[] sizeNames = {"A4", "Letter", "A3"};
        
        for (int i = 0; i < pageSizes.length; i++) {
            File inputPdf = createSamplePdfWithPageSize("demo-" + sizeNames[i].toLowerCase() + "-input.pdf", 
                    sizeNames[i] + " Page Size Test", 
                    "This document uses " + sizeNames[i] + " page size.", 
                    "The scanned version should maintain the same dimensions.", 
                    pageSizes[i]);
            
            long startTime = System.currentTimeMillis();
            byte[] scannedPdfBytes = service.convertToScannedPdf(inputPdf);
            long duration = System.currentTimeMillis() - startTime;
            
            File outputPdf = tempDir.resolve("demo-output-" + sizeNames[i].toLowerCase() + "-scanned.pdf").toFile();
            Files.write(outputPdf.toPath(), scannedPdfBytes);
            
            System.out.println("   ✓ " + sizeNames[i] + " size: " + outputPdf.getName() + 
                             " (" + scannedPdfBytes.length + " bytes, " + duration + "ms)");
        }
    }

    private File createSamplePdf(String filename, String title, String line1, String line2) throws IOException {
        return createSamplePdfWithPageSize(filename, title, line1, line2, PDRectangle.A4);
    }

    private File createSamplePdfWithPageSize(String filename, String title, String line1, String line2, PDRectangle pageSize) throws IOException {
        File pdfFile = tempDir.resolve(filename).toFile();
        
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage(pageSize);
            document.addPage(page);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // Title
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 16);
                contentStream.newLineAtOffset(50, pageSize.getHeight() - 100);
                contentStream.showText(title);
                contentStream.endText();
                
                // Content
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 12);
                contentStream.newLineAtOffset(50, pageSize.getHeight() - 150);
                contentStream.showText(line1);
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText(line2);
                contentStream.newLineAtOffset(0, -40);
                contentStream.showText("Generated at: " + new java.util.Date());
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText("Page size: " + pageSize.getWidth() + " x " + pageSize.getHeight() + " points");
                contentStream.endText();
                
                // Add some visual elements
                contentStream.setLineWidth(2);
                contentStream.moveTo(50, pageSize.getHeight() - 200);
                contentStream.lineTo(pageSize.getWidth() - 50, pageSize.getHeight() - 200);
                contentStream.stroke();
                
                // Add a rectangle
                contentStream.addRect(50, 50, 100, 50);
                contentStream.stroke();
                
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 10);
                contentStream.newLineAtOffset(55, 70);
                contentStream.showText("Sample Box");
                contentStream.endText();
            }
            
            document.save(pdfFile);
        }
        
        return pdfFile;
    }
}
