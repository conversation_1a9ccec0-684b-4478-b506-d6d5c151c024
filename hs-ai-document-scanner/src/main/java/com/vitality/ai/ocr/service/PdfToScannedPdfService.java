package com.vitality.ai.ocr.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for converting regular PDF documents into "scanned" PDFs where each page
 * is rendered as a raster image. This simulates the effect of physically scanning
 * a document, useful for testing OCR services or document processing workflows.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PdfToScannedPdfService {

    private static final int DEFAULT_DPI = 300;
    private static final String IMAGE_FORMAT = "png";

    /**
     * Convert a PDF file to a "scanned" PDF where each page is rendered as an image.
     *
     * @param inputFile The input PDF file
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    public byte[] convertToScannedPdf(File inputFile) throws IOException {
        return convertToScannedPdf(inputFile, DEFAULT_DPI);
    }

    /**
     * Convert a PDF file to a "scanned" PDF with specified DPI.
     *
     * @param inputFile The input PDF file
     * @param dpi The resolution for rendering pages (minimum 150, recommended 300+)
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    public byte[] convertToScannedPdf(File inputFile, int dpi) throws IOException {
        validateInputs(inputFile, dpi);

        log.info("Starting conversion of PDF to scanned format: {} at {} DPI",
                inputFile.getName(), dpi);

        try (PDDocument inputDocument = Loader.loadPDF(inputFile)) {
            return convertDocumentToScannedPdf(inputDocument, dpi);
        } catch (Exception e) {
            log.error("Error converting PDF to scanned format: {}", e.getMessage(), e);
            throw new IOException("Failed to convert PDF to scanned format", e);
        }
    }

    /**
     * Convert a PDF from InputStream to a "scanned" PDF.
     *
     * @param inputStream The input PDF stream
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    public byte[] convertToScannedPdf(InputStream inputStream) throws IOException {
        return convertToScannedPdf(inputStream, DEFAULT_DPI);
    }

    /**
     * Convert a PDF from InputStream to a "scanned" PDF with specified DPI.
     *
     * @param inputStream The input PDF stream
     * @param dpi The resolution for rendering pages
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    public byte[] convertToScannedPdf(InputStream inputStream, int dpi) throws IOException {
        validateDpi(dpi);

        log.info("Starting conversion of PDF stream to scanned format at {} DPI", dpi);

        // Convert InputStream to byte array since PDFBox Loader doesn't accept InputStream directly
        byte[] inputBytes;
        try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
            inputStream.transferTo(buffer);
            inputBytes = buffer.toByteArray();
        }

        try (PDDocument inputDocument = Loader.loadPDF(inputBytes)) {
            return convertDocumentToScannedPdf(inputDocument, dpi);
        } catch (Exception e) {
            log.error("Error converting PDF stream to scanned format: {}", e.getMessage(), e);
            throw new IOException("Failed to convert PDF stream to scanned format", e);
        }
    }

    /**
     * Convert a PDF from byte array to a "scanned" PDF.
     *
     * @param inputBytes The input PDF as byte array
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    public byte[] convertToScannedPdf(byte[] inputBytes) throws IOException {
        return convertToScannedPdf(inputBytes, DEFAULT_DPI);
    }

    /**
     * Convert a PDF from byte array to a "scanned" PDF with specified DPI.
     *
     * @param inputBytes The input PDF as byte array
     * @param dpi The resolution for rendering pages
     * @return Byte array containing the scanned PDF
     * @throws IOException If there's an error processing the PDF
     */
    public byte[] convertToScannedPdf(byte[] inputBytes, int dpi) throws IOException {
        validateInputs(inputBytes, dpi);

        log.info("Starting conversion of PDF byte array to scanned format at {} DPI", dpi);

        try (PDDocument inputDocument = Loader.loadPDF(inputBytes)) {
            return convertDocumentToScannedPdf(inputDocument, dpi);
        } catch (Exception e) {
            log.error("Error converting PDF bytes to scanned format: {}", e.getMessage(), e);
            throw new IOException("Failed to convert PDF bytes to scanned format", e);
        }
    }

    /**
     * Write the scanned PDF to an OutputStream.
     *
     * @param inputFile The input PDF file
     * @param outputStream The output stream to write to
     * @param dpi The resolution for rendering pages
     * @throws IOException If there's an error processing the PDF
     */
    public void convertToScannedPdf(File inputFile, OutputStream outputStream, int dpi) throws IOException {
        byte[] scannedPdfBytes = convertToScannedPdf(inputFile, dpi);
        outputStream.write(scannedPdfBytes);
        outputStream.flush();

        log.info("Successfully wrote scanned PDF to output stream");
    }

    /**
     * Core conversion logic that processes the PDDocument.
     */
    private byte[] convertDocumentToScannedPdf(PDDocument inputDocument, int dpi) throws IOException {
        int pageCount = inputDocument.getNumberOfPages();
        log.info("Processing {} pages for conversion", pageCount);

        // Render all pages as images
        List<BufferedImage> pageImages = renderPagesToImages(inputDocument, dpi);

        // Create new PDF with images
        try (PDDocument outputDocument = new PDDocument()) {
            for (int i = 0; i < pageImages.size(); i++) {
                BufferedImage pageImage = pageImages.get(i);
                PDPage originalPage = inputDocument.getPage(i);

                addImagePageToDocument(outputDocument, pageImage, originalPage.getMediaBox());

                log.debug("Processed page {} of {}", i + 1, pageCount);
            }

            // Convert to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            outputDocument.save(outputStream);

            log.info("Successfully converted {} pages to scanned PDF format", pageCount);
            return outputStream.toByteArray();
        }
    }

    /**
     * Render all pages of the document to BufferedImages.
     */
    private List<BufferedImage> renderPagesToImages(PDDocument document, int dpi) throws IOException {
        PDFRenderer renderer = new PDFRenderer(document);
        List<BufferedImage> images = new ArrayList<>();

        for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
            BufferedImage pageImage = renderer.renderImageWithDPI(pageIndex, dpi);
            images.add(pageImage);
        }

        return images;
    }

    /**
     * Add a BufferedImage as a page to the output PDF document.
     */
    private void addImagePageToDocument(PDDocument document, BufferedImage image, PDRectangle originalPageSize) throws IOException {
        // Convert BufferedImage to byte array
        ByteArrayOutputStream imageStream = new ByteArrayOutputStream();
        ImageIO.write(image, IMAGE_FORMAT, imageStream);
        byte[] imageBytes = imageStream.toByteArray();

        // Create PDImageXObject from the image bytes
        PDImageXObject pdImage = PDImageXObject.createFromByteArray(document, imageBytes, "page_image");

        // Create new page with original dimensions
        PDPage page = new PDPage(originalPageSize);
        document.addPage(page);

        // Calculate scaling to fit image to page while maintaining aspect ratio
        float pageWidth = originalPageSize.getWidth();
        float pageHeight = originalPageSize.getHeight();
        float imageWidth = pdImage.getWidth();
        float imageHeight = pdImage.getHeight();

        float scaleX = pageWidth / imageWidth;
        float scaleY = pageHeight / imageHeight;
        float scale = Math.min(scaleX, scaleY);

        float scaledWidth = imageWidth * scale;
        float scaledHeight = imageHeight * scale;

        // Center the image on the page
        float x = (pageWidth - scaledWidth) / 2;
        float y = (pageHeight - scaledHeight) / 2;

        // Draw the image on the page
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            contentStream.drawImage(pdImage, x, y, scaledWidth, scaledHeight);
        }
    }

    /**
     * Validate input parameters.
     */
    private void validateInputs(File inputFile, int dpi) throws IOException {
        if (inputFile == null) {
            throw new IllegalArgumentException("Input file cannot be null");
        }
        if (!inputFile.exists()) {
            throw new FileNotFoundException("Input file does not exist: " + inputFile.getPath());
        }
        if (!inputFile.canRead()) {
            throw new IOException("Cannot read input file: " + inputFile.getPath());
        }
        validateDpi(dpi);
    }

    private void validateInputs(byte[] inputBytes, int dpi) {
        if (inputBytes == null || inputBytes.length == 0) {
            throw new IllegalArgumentException("Input bytes cannot be null or empty");
        }
        validateDpi(dpi);
    }

    private void validateDpi(int dpi) {
        if (dpi < 150) {
            throw new IllegalArgumentException("DPI must be at least 150 for readable output. Recommended: 300+");
        }
        if (dpi > 600) {
            log.warn("High DPI ({}) may result in very large file sizes and slow processing", dpi);
        }
    }
}
