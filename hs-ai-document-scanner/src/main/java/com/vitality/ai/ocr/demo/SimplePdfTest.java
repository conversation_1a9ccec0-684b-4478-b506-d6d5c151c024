package com.vitality.ai.ocr.demo;

import com.vitality.ai.ocr.service.PdfToScannedPdfService;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * Simple test for the PdfToScannedPdfService without Spring Boot dependencies.
 */
public class SimplePdfTest {

    public static void main(String[] args) {
        System.out.println("🚀 Testing PDF to Scanned PDF Service...\n");
        
        try {
            SimplePdfTest test = new SimplePdfTest();
            test.runTest();
            System.out.println("\n✅ Test completed successfully!");
            
        } catch (Exception e) {
            System.err.println("\n❌ Test failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    public void runTest() throws IOException {
        // Create temporary directory
        Path tempDir = Files.createTempDirectory("pdf-test");
        System.out.println("📁 Working directory: " + tempDir.toAbsolutePath());
        
        // Create service instance
        PdfToScannedPdfService service = new PdfToScannedPdfService();
        
        // Create a sample PDF
        File inputPdf = createSamplePdf(tempDir, "test-input.pdf");
        System.out.println("📄 Created sample PDF: " + inputPdf.getName() + " (" + inputPdf.length() + " bytes)");
        
        // Convert to scanned PDF
        System.out.println("🔄 Converting to scanned PDF...");
        long startTime = System.currentTimeMillis();
        byte[] scannedPdfBytes = service.convertToScannedPdf(inputPdf);
        long duration = System.currentTimeMillis() - startTime;
        
        // Save the result
        File outputPdf = tempDir.resolve("test-output-scanned.pdf").toFile();
        Files.write(outputPdf.toPath(), scannedPdfBytes);
        
        // Verify the result
        try (PDDocument document = Loader.loadPDF(scannedPdfBytes)) {
            System.out.println("✅ Conversion successful!");
            System.out.println("   📊 Input size: " + inputPdf.length() + " bytes");
            System.out.println("   📊 Output size: " + scannedPdfBytes.length + " bytes");
            System.out.println("   📊 Pages: " + document.getNumberOfPages());
            System.out.println("   ⏱️ Processing time: " + duration + "ms");
            System.out.println("   📁 Output file: " + outputPdf.getAbsolutePath());
        }
        
        // Test different DPI settings
        testDifferentDpi(service, inputPdf, tempDir);
        
        // Test InputStream conversion
        testInputStreamConversion(service, inputPdf, tempDir);
        
        System.out.println("\n🎉 All tests passed! Files saved in: " + tempDir.toAbsolutePath());
    }

    private void testDifferentDpi(PdfToScannedPdfService service, File inputPdf, Path tempDir) throws IOException {
        System.out.println("\n🔧 Testing different DPI settings...");
        
        int[] dpiSettings = {150, 300, 600};
        
        for (int dpi : dpiSettings) {
            long startTime = System.currentTimeMillis();
            byte[] scannedPdfBytes = service.convertToScannedPdf(inputPdf, dpi);
            long duration = System.currentTimeMillis() - startTime;
            
            File outputPdf = tempDir.resolve("test-output-" + dpi + "dpi.pdf").toFile();
            Files.write(outputPdf.toPath(), scannedPdfBytes);
            
            System.out.println("   ✅ " + dpi + " DPI: " + scannedPdfBytes.length + " bytes (" + duration + "ms)");
        }
    }

    private void testInputStreamConversion(PdfToScannedPdfService service, File inputPdf, Path tempDir) throws IOException {
        System.out.println("\n🌊 Testing InputStream conversion...");
        
        try (var inputStream = Files.newInputStream(inputPdf.toPath())) {
            long startTime = System.currentTimeMillis();
            byte[] scannedPdfBytes = service.convertToScannedPdf(inputStream);
            long duration = System.currentTimeMillis() - startTime;
            
            File outputPdf = tempDir.resolve("test-output-from-stream.pdf").toFile();
            Files.write(outputPdf.toPath(), scannedPdfBytes);
            
            System.out.println("   ✅ Stream conversion: " + scannedPdfBytes.length + " bytes (" + duration + "ms)");
        }
    }

    private File createSamplePdf(Path tempDir, String filename) throws IOException {
        File pdfFile = tempDir.resolve(filename).toFile();
        
        try (PDDocument document = new PDDocument()) {
            // Create first page
            PDPage page1 = new PDPage(PDRectangle.A4);
            document.addPage(page1);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page1)) {
                // Title
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 18);
                contentStream.newLineAtOffset(50, 750);
                contentStream.showText("PDF to Scanned PDF Test Document");
                contentStream.endText();
                
                // Content
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 12);
                contentStream.newLineAtOffset(50, 700);
                contentStream.showText("This is a test document that will be converted to a scanned PDF.");
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText("The original contains selectable text and vector graphics.");
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText("After conversion, it will contain only raster images.");
                contentStream.newLineAtOffset(0, -40);
                contentStream.showText("Generated at: " + new java.util.Date());
                contentStream.endText();
                
                // Add some visual elements
                contentStream.setLineWidth(2);
                contentStream.moveTo(50, 600);
                contentStream.lineTo(550, 600);
                contentStream.stroke();
                
                // Add a rectangle
                contentStream.addRect(50, 500, 200, 80);
                contentStream.stroke();
                
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 14);
                contentStream.newLineAtOffset(60, 530);
                contentStream.showText("Sample Box with Text");
                contentStream.endText();
            }
            
            // Create second page
            PDPage page2 = new PDPage(PDRectangle.A4);
            document.addPage(page2);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page2)) {
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 16);
                contentStream.newLineAtOffset(50, 750);
                contentStream.showText("Page 2 - Additional Content");
                contentStream.endText();
                
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 12);
                contentStream.newLineAtOffset(50, 700);
                contentStream.showText("This is the second page of the test document.");
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText("It demonstrates multi-page PDF conversion.");
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText("Both pages will be converted to images in the output PDF.");
                contentStream.endText();
                
                // Add a circle (approximated with curves)
                float centerX = 300;
                float centerY = 500;
                float radius = 50;
                
                contentStream.moveTo(centerX + radius, centerY);
                contentStream.curveTo(centerX + radius, centerY + radius * 0.552f,
                                    centerX + radius * 0.552f, centerY + radius,
                                    centerX, centerY + radius);
                contentStream.curveTo(centerX - radius * 0.552f, centerY + radius,
                                    centerX - radius, centerY + radius * 0.552f,
                                    centerX - radius, centerY);
                contentStream.curveTo(centerX - radius, centerY - radius * 0.552f,
                                    centerX - radius * 0.552f, centerY - radius,
                                    centerX, centerY - radius);
                contentStream.curveTo(centerX + radius * 0.552f, centerY - radius,
                                    centerX + radius, centerY - radius * 0.552f,
                                    centerX + radius, centerY);
                contentStream.stroke();
                
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 10);
                contentStream.newLineAtOffset(centerX - 20, centerY - 5);
                contentStream.showText("Circle");
                contentStream.endText();
            }
            
            document.save(pdfFile);
        }
        
        return pdfFile;
    }
}
