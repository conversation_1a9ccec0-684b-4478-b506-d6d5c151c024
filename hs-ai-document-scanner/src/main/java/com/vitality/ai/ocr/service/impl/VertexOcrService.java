package com.vitality.ai.ocr.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.vitality.ai.ocr.PromptBuilder;
import com.vitality.ai.ocr.model.survey.form.HraForm;
import com.vitality.ai.ocr.prompt.model.OcrResponse;
import com.vitality.ai.ocr.prompt.model.response.MedicalForm;
import com.vitality.ai.ocr.service.PdfImageExtractor;
import com.vitality.ai.ocr.service.SmartOCRImageProcessor;
import com.vitality.ai.ocr.service.VitalityChatService;
import com.vitality.ai.ocr.util.ImageUtils;
import dev.langchain4j.data.message.*;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.vitality.ai.ocr.util.ImageUtils.performDebugSave;

@Service
@Slf4j
@RequiredArgsConstructor
public class VertexOcrService implements VitalityChatService {

    private final ChatModel chatModel;
    private final PdfImageExtractor pdfExtractor;
    private String generatePrompt = null;

    /**
     * Perform OCR on a file (PDF or image)
     */
    @Override
    public OcrResponse performOcr(MultipartFile file, String documentType) throws IOException {
        String contentType = file.getContentType();
//        documentType needs to be the document type, like hra
        // Convert MultipartFile to temp File
        File tempFile = convertMultiPartToFile(file);

        try {
            // If it's a PDF, extract images from it
            if (contentType != null && contentType.toLowerCase().contains("pdf")) {
                return processPdfFile(tempFile);
            } else {
                // It's an image file, process directly
                throw new IllegalArgumentException("Unsupported content type: " + contentType);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            // Clean up the temp file
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }


    private String getPrompt() throws JsonProcessingException {
        if (generatePrompt == null) {

            final ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
            String jsonString = objectMapper.writeValueAsString(MedicalForm.getDummy());
            String crossRefJson = objectMapper.writeValueAsString(HraForm.getHraTemplate());

            final String prompt = PromptBuilder.create()
                    .withSystemContext("You are an OCR engine that will extract text from images.")
                    .withInstruction("Take the image input and extract all text from it.")
                    .withInstruction("If there is an answer to a question, extract the answer from the input provided,  the question and only the ANSWERED VALUE to the value in the selectedAnswer tag.")
                    .withInstruction("If there is no answer to a question, still extract the question with the value in the selectedAnswer tag as NOT ANSWERED.")
                    .withInstruction("If there is any doubt about the answer, indicate which question number and the question with the value in the selectedAnswer tag as DOUBT.")
                    .withInstruction("Only answered questions where the box is checked should be marked as SELECTED.")
                    .withInstruction("For answers that have a checkbox, there has to be definitive selection made, a definitive selection is a marking in the box, if not then the value in the selectedAnswer tag should be NOT ANSWERED.")
                    .withInstruction("You are to response with complete JSON, no short responses. Every Image is to be processed")
                    .withInstruction("Use the following cross reference structure to map the question Id to the shortToken when responding to the text matched: " + crossRefJson)
                    .withOutput("Structure the response as a JSON object with the following structure:" + jsonString)
                    .build();
            generatePrompt = prompt;
        }
        return generatePrompt;
    }

    /**
     * Process a PDF file by extracting its pages as images
     */
    private OcrResponse processPdfFile(File pdfFile) throws Exception {
        // Get page count
        int pageCount = pdfExtractor.getPageCount(pdfFile);
        final ArrayList<String> extractedDetails = extractBase64Images(pdfFile, pageCount);
        // Create system message

        final String prompt = getPrompt();

        final SystemMessage systemMessage = SystemMessage.from(prompt);

        final ArrayList<Content> contents = new ArrayList<>();
        contents.add(new TextContent("Extract all text from this document"));

        extractedDetails.forEach(s -> {
            contents.add(new ImageContent(s, "image/png"));
        });

        // Create user message with the image
        final UserMessage userMessage = UserMessage.from(contents);

        final List<ChatMessage> chatMessageList = List.of(systemMessage, userMessage);

        final ChatResponse chat = chatModel.chat(chatMessageList);
        final String text = chat.aiMessage().text();

        log.info(chat.toString());

        final OcrResponse ocrResponse = new OcrResponse();
        ocrResponse.setRawText(text);
        ocrResponse.setPageCount(pageCount);
        ocrResponse.setMedicalForm(extractJsonAndParse(text));
        log.info(ocrResponse.toString());
        return ocrResponse;
    }

    private ArrayList<String> extractBase64Images(File pdfFile, int pageCount) throws IOException {
        final ArrayList<String> extractedDetails = new ArrayList<>(pageCount);
        for (int i = 0; i < pageCount; i++) {
            final String pageImage = pdfExtractor.extractPageAsImage(pdfFile, i, 300); // 300 DPI
            if (log.isDebugEnabled()) {
                performDebugSave(i, pageImage, "image/png");
            }
            extractedDetails.add(pageImage);
        }
        return extractedDetails;
    }

    /**
     * Extracts JSON text wrapped in markdown code blocks and parses it to a MedicalForm object
     *
     * @param markdownText Text containing JSON wrapped in markdown code blocks (```json ... ```)
     * @return Parsed MedicalForm object
     * @throws Exception If parsing fails
     */
    public static MedicalForm extractJsonAndParse(String markdownText) throws Exception {
        // Extract JSON content from markdown code blocks
        String jsonContent = extractJsonFromMarkdown(markdownText);

        if (jsonContent == null || jsonContent.trim().isEmpty()) {
            throw new IllegalArgumentException("No valid JSON content found in the provided text");
        }

        // Parse JSON to MedicalForm object
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(jsonContent, MedicalForm.class);
    }

    /**
     * Extracts JSON content from markdown code blocks
     *
     * @param markdownText Text containing JSON wrapped in markdown code blocks
     * @return Extracted JSON string or null if not found
     */
    public static String extractJsonFromMarkdown(String markdownText) {
        if (markdownText == null || markdownText.trim().isEmpty()) {
            return null;
        }

        // Pattern to match content between ```json and ``` tags
        // This handles both complete and incomplete code blocks
        Pattern pattern = Pattern.compile("```json\\s*\\n(.*?)(?:```|$)", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(markdownText);

        if (matcher.find()) {
            String extractedJson = matcher.group(1).trim();

            // Handle potentially incomplete JSON by trying to complete it
            // This is a basic approach - more sophisticated solutions might be needed for complex cases
            if (!extractedJson.endsWith("}")) {
                // Check if we have an opening brace that needs closing
                int openBraces = countCharacter(extractedJson, '{');
                int closeBraces = countCharacter(extractedJson, '}');

                if (openBraces > closeBraces) {
                    // Add missing closing braces
                    for (int i = 0; i < (openBraces - closeBraces); i++) {
                        extractedJson += "}";
                    }
                }
            }

            return extractedJson;
        }

        return null;
    }

    /**
     * Counts occurrences of a specific character in a string
     *
     * @param str String to search in
     * @param charToCount Character to count
     * @return Number of occurrences
     */
    private static int countCharacter(String str, char charToCount) {
        return (int) str.chars().filter(c -> c == charToCount).count();
    }

    /**
     * Convert MultipartFile to File
     */
    private File convertMultiPartToFile(MultipartFile file) throws IOException {
        File tempFile = File.createTempFile("upload-", "-temp");
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(file.getBytes());
        }
        return tempFile;
    }
}
