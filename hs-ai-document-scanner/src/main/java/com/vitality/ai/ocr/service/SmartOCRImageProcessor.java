package com.vitality.ai.ocr.service;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Base64;
import java.util.concurrent.*;

/**
 * OCR-focused image processor that:
 * - Applies optimal filters: grayscale, denoise, contrast stretch, thresholding, deskew
 * - Enhances font blackness for better text recognition
 * - Scales the image to a specified percentage (as the final step)
 * - 100% pure Java, no external libraries
 */
@Slf4j
public class SmartOCRImageProcessor {

    /**
     * Main entry point. Accepts a byte[] image, applies OCR-optimized filtering pipeline,
     * and returns a binarized, deskewed image as byte[].
     *
     * @param inputBytes The input image bytes
     * @return Processed image bytes
     * @throws IOException If there's an error processing the image
     */
    public static byte[] process(int imageNumber, byte[] inputBytes) throws IOException {
        return process(imageNumber, inputBytes, 100); // Default: no scaling
    }

    /**
     * Processes the image with optional scaling.
     *
     * @param inputBytes The input image bytes
     * @param scalePercent The percentage to scale the image (50 = half size, 200 = double size)
     * @return Processed image bytes
     * @throws IOException If there's an error processing the image
     */
    public static byte[] process(int imageNumber, byte[] inputBytes, int scalePercent) throws IOException {
        BufferedImage original = ImageIO.read(new ByteArrayInputStream(inputBytes));
        if (original == null) throw new IllegalArgumentException("Invalid image");

        BufferedImage gray = toGrayscale(original);
        Grade grade = gradeImage(gray);
        performDebugSave(imageNumber,gray,"gray"); // Save grayscale image for debugging
        // Apply pipeline based on image quality
        if (grade.noiseLevel > 5) {
            gray = denoise(gray);
            performDebugSave(imageNumber,gray,"denoise"); // Save grayscale image for debugging
        }

        if (grade.contrast < 30 || grade.brightness < 80 || grade.brightness > 200) {
            gray = stretchContrast(gray);
            performDebugSave(imageNumber,gray,"contrast"); // Save contrast-stretched image for debugging
        }

        BufferedImage binary = gray;//grade.hasUnevenLighting
//                ? adaptiveBinarizeParallel(gray, 15, 10)
//                : binarize(gray, 128);

        performDebugSave(imageNumber,binary,"binarize"); // Save binary image for debugging
        // Enhance font blackness for better OCR
//        binary = enhanceFontBlackness(binary);
//        performDebugSave(imageNumber,binary,"font"); // Save enhanced binary image for debugging

        double skewAngle = detectSkewAngle(binary);
        if (Math.abs(skewAngle) > 0.5) {
            binary = rotateImage(binary, -skewAngle); // Deskew
            performDebugSave(imageNumber,binary, "rotate"); // Save deskewed image for debugging
        }

        // Scale as the final step (moved from beginning to end)
        if (scalePercent != 100) {
            binary = scaleImage(binary, scalePercent);
            performDebugSave(imageNumber,binary,"scale"); // Save scaled image for debugging
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ImageIO.write(binary, "png", out);

        return out.toByteArray();
    }

    public static void performDebugSave(int imageNumber, BufferedImage bufferedImage, String name) {
        if (log.isDebugEnabled()) {
            try {
                Path debugDir = Paths.get("/Users/<USER>/development/research/chatAgent/hs-document-scanner/src/main/resources/debug");
                if (!Files.exists(debugDir)) {
                    Files.createDirectories(debugDir);
                }

                String timestamp = imageNumber + "";
                String extension = "png";
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "png", out);
                Path imageFile = debugDir.resolve(timestamp + "_" + name + "_debug_image"+ "." + extension);
                Files.write(imageFile, out.toByteArray());
                log.info("Debug image saved to: {}", imageFile.toAbsolutePath());

            } catch (IOException e) {
                log.error("Failed to save debug image file", e);
            }
        }
    }

    /**
     * Enhances the blackness of text in binary images by applying
     * morphological operations to fill small gaps in characters.
     *
     * @param binary The binary image to enhance
     * @return The enhanced binary image with improved font blackness
     */
    private static BufferedImage enhanceFontBlackness(BufferedImage binary) {
        int w = binary.getWidth(), h = binary.getHeight();
        WritableRaster raster = binary.getRaster();
        BufferedImage enhanced = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_BINARY);
        WritableRaster outRaster = enhanced.getRaster();

        // First pass: apply closing operation to fill in gaps in characters
        // Uses a 2x2 structuring element for better character connectivity
        for (int y = 1; y < h - 1; y++) {
            for (int x = 1; x < w - 1; x++) {
                // Check if this pixel or any of its immediate neighbors are black (0)
                boolean hasBlackNeighbor = false;

                // Only process white pixels
                if (raster.getSample(x, y, 0) == 1) {
                    // Check 3x3 neighborhood for black pixels
                    for (int dy = -1; dy <= 1 && !hasBlackNeighbor; dy++) {
                        for (int dx = -1; dx <= 1 && !hasBlackNeighbor; dx++) {
                            if (raster.getSample(x + dx, y + dy, 0) == 0) {
                                hasBlackNeighbor = true;
                            }
                        }
                    }

                    // If at least one neighbor is black, check if this pixel should be filled
                    // We use a more aggressive approach for horizontal/vertical neighbors
                    if (hasBlackNeighbor) {
                        // Count black neighbors in cardinal directions (more important for text)
                        int cardinalBlackCount = 0;
                        if (raster.getSample(x - 1, y, 0) == 0) cardinalBlackCount++;
                        if (raster.getSample(x + 1, y, 0) == 0) cardinalBlackCount++;
                        if (raster.getSample(x, y - 1, 0) == 0) cardinalBlackCount++;
                        if (raster.getSample(x, y + 1, 0) == 0) cardinalBlackCount++;

                        // If we have at least 2 cardinal black neighbors, make this pixel black
                        if (cardinalBlackCount >= 2) {
                            outRaster.setSample(x, y, 0, 0); // Set to black
                            continue;
                        }
                    }
                }

                // Default: copy the original pixel
                outRaster.setSample(x, y, 0, raster.getSample(x, y, 0));
            }
        }

        // Copy edges from original
        for (int y = 0; y < h; y++) {
            outRaster.setSample(0, y, 0, raster.getSample(0, y, 0));
            outRaster.setSample(w - 1, y, 0, raster.getSample(w - 1, y, 0));
        }
        for (int x = 1; x < w - 1; x++) {
            outRaster.setSample(x, 0, 0, raster.getSample(x, 0, 0));
            outRaster.setSample(x, h - 1, 0, raster.getSample(x, h - 1, 0));
        }

        return enhanced;
    }

    /**
     * Scales an image to a specified percentage of its original size.
     *
     * @param image The image to scale
     * @param scalePercent The percentage to scale (50 = half size, 200 = double size)
     * @return The scaled image
     */
    private static BufferedImage scaleImage(BufferedImage image, int scalePercent) {
        if (scalePercent <= 0) throw new IllegalArgumentException("Scale percentage must be positive");
        if (scalePercent == 100) return image; // No scaling needed

        int targetWidth = (int) (image.getWidth() * scalePercent / 100.0);
        int targetHeight = (int) (image.getHeight() * scalePercent / 100.0);

        // Use faster nearest neighbor scaling for binary images (better for OCR text)
        // This preserves the sharp edges of text better than bilinear
        Object hint = RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR;

        BufferedImage scaled = new BufferedImage(targetWidth, targetHeight, image.getType());
        Graphics2D g = scaled.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, hint);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);
        g.drawImage(image, 0, 0, targetWidth, targetHeight, null);
        g.dispose();

        return scaled;
    }

    /**
     * Converts any BufferedImage to grayscale using AWT Graphics2D rendering.
     */
    private static BufferedImage toGrayscale(BufferedImage src) {
        // Fast path: if already grayscale, return original
        if (src.getType() == BufferedImage.TYPE_BYTE_GRAY) {
            return src;
        }

        BufferedImage gray = new BufferedImage(src.getWidth(), src.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g = gray.createGraphics();
        g.drawImage(src, 0, 0, null);
        g.dispose();
        return gray;
    }

    /**
     * Grades an image by evaluating brightness, contrast, noise, and uneven lighting.
     * Uses optimized batch sampling and avoids pixel-by-pixel loops.
     */
    private static Grade gradeImage(BufferedImage gray) {
        int w = gray.getWidth(), h = gray.getHeight();

        // Use DataBuffer directly for faster pixel access
        DataBuffer buffer = gray.getRaster().getDataBuffer();
        int numPixels = w * h;

        // Calculate using single pass algorithm
        long sum = 0, sumSq = 0;
        for (int i = 0; i < numPixels; i++) {
            int p = buffer.getElem(i);
            sum += p;
            sumSq += (long) p * p;
        }

        double mean = sum / (double) numPixels;
        double variance = (sumSq - ((double) sum * sum) / numPixels) / numPixels;
        double stdDev = Math.sqrt(variance);

        Raster raster = gray.getRaster();
        boolean uneven = checkLightingFast(raster, w, h, mean);
        int noise = estimateNoiseFast(raster, w, h);

        return new Grade((int) mean, (int) stdDev, uneven, noise);
    }

    /**
     * Detects uneven lighting across 4 corner blocks using mean pixel intensity and checks variation.
     */
    private static boolean checkLightingFast(Raster raster, int w, int h, double globalMean) {
        int blockSize = Math.min(16, Math.min(w, h) / 8); // Adaptive block size
        int[][] blocks = new int[4][];
        blocks[0] = getBlockSamples(raster, 0, 0, blockSize);
        blocks[1] = getBlockSamples(raster, w - blockSize, 0, blockSize);
        blocks[2] = getBlockSamples(raster, 0, h - blockSize, blockSize);
        blocks[3] = getBlockSamples(raster, w - blockSize, h - blockSize, blockSize);
        double[] means = Arrays.stream(blocks).mapToDouble(SmartOCRImageProcessor::meanOf).toArray();
        double min = Arrays.stream(means).min().orElse(globalMean);
        double max = Arrays.stream(means).max().orElse(globalMean);
        return (max - min) > globalMean * 0.25;
    }

    /**
     * Extracts a square block of pixels from the raster at the given position.
     */
    private static int[] getBlockSamples(Raster r, int x, int y, int size) {
        int[] samples = new int[size * size];
        r.getSamples(x, y, size, size, 0, samples);
        return samples;
    }

    /**
     * Computes the mean (average) of an int array.
     */
    private static double meanOf(int[] data) {
        long sum = 0;
        for (int value : data) {
            sum += value;
        }
        return sum / (double) data.length;
    }

    /**
     * Estimates noise by measuring gradient differences on a fixed pixel stride grid.
     * High differences imply speckle noise or blur artifacts.
     */
    private static int estimateNoiseFast(Raster raster, int w, int h) {
        // Adaptive stride based on image size
        int stride = Math.max(1, Math.min(w, h) / 100);
        int count = 0;
        int[] neighborhood = new int[5]; // Center + 4 neighbors

        for (int y = stride; y < h - stride; y += stride) {
            for (int x = stride; x < w - stride; x += stride) {
                neighborhood[0] = raster.getSample(x, y, 0);      // Center
                neighborhood[1] = raster.getSample(x - stride, y, 0);  // Left
                neighborhood[2] = raster.getSample(x + stride, y, 0);  // Right
                neighborhood[3] = raster.getSample(x, y - stride, 0);  // Top
                neighborhood[4] = raster.getSample(x, y + stride, 0);  // Bottom

                int center = neighborhood[0];
                int diff = Math.abs(center - neighborhood[1]) +
                        Math.abs(center - neighborhood[2]) +
                        Math.abs(center - neighborhood[3]) +
                        Math.abs(center - neighborhood[4]);

                if (diff > 100) count++;
            }
        }
        return count;
    }

    /**
     * Applies a 3x3 median filter to denoise the grayscale image.
     * Pure Java implementation using fast sorting.
     */
    private static BufferedImage denoise(BufferedImage gray) {
        int w = gray.getWidth(), h = gray.getHeight();
        BufferedImage out = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = gray.getRaster(), outRaster = out.getRaster();
        int[] medianBuffer = new int[9];

        // Skip edges to avoid bounds checking
        for (int y = 1; y < h - 1; y++) {
            for (int x = 1; x < w - 1; x++) {
                int idx = 0;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        medianBuffer[idx++] = inRaster.getSample(x + dx, y + dy, 0);
                    }
                }

                // Fast median of 9 elements - partial sort
                for (int i = 0; i < 5; i++) {
                    int min_idx = i;
                    for (int j = i + 1; j < 9; j++) {
                        if (medianBuffer[j] < medianBuffer[min_idx]) {
                            min_idx = j;
                        }
                    }
                    // Swap
                    if (min_idx != i) {
                        int temp = medianBuffer[i];
                        medianBuffer[i] = medianBuffer[min_idx];
                        medianBuffer[min_idx] = temp;
                    }
                }

                outRaster.setSample(x, y, 0, medianBuffer[4]);
            }
        }

        // Copy edges from original
        for (int y = 0; y < h; y++) {
            outRaster.setSample(0, y, 0, inRaster.getSample(0, y, 0));
            outRaster.setSample(w - 1, y, 0, inRaster.getSample(w - 1, y, 0));
        }
        for (int x = 1; x < w - 1; x++) {
            outRaster.setSample(x, 0, 0, inRaster.getSample(x, 0, 0));
            outRaster.setSample(x, h - 1, 0, inRaster.getSample(x, h - 1, 0));
        }

        return out;
    }

    /**
     * Performs contrast stretching (histogram normalization) by stretching pixel intensities
     * between the image's min and max brightness.
     */
    private static BufferedImage stretchContrast(BufferedImage gray) {
        int w = gray.getWidth(), h = gray.getHeight();
        WritableRaster in = gray.getRaster();
        WritableRaster out = in.createCompatibleWritableRaster();

        // Use a histogram for faster min/max calculation
        int[] histogram = new int[256];
        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                histogram[in.getSample(x, y, 0)]++;
            }
        }

        // Find min/max values from histogram
        int min = 0;
        while (min < 255 && histogram[min] == 0) min++;

        int max = 255;
        while (max > 0 && histogram[max] == 0) max--;

        // Skip if no stretch needed
        if (min == 0 && max == 255) {
            return gray;
        }

        // Prepare lookup table for faster processing
        byte[] lut = new byte[256];
        if (max > min) {
            float scale = 255f / (max - min);
            for (int i = 0; i < 256; i++) {
                lut[i] = (byte) (i <= min ? 0 : (i >= max ? 255 : Math.round((i - min) * scale)));
            }
        } else {
            // Edge case: all pixels same value
            Arrays.fill(lut, (byte) 128);
        }

        // Apply lookup table transform
        LookupOp op = new LookupOp(new ByteLookupTable(0, lut), null);
        return op.filter(gray, null);
    }

    /**
     * Calculates the optimal threshold using Otsu's method.
     * This is more adaptive than a fixed threshold.
     */
    private static int calculateOptimalThreshold(BufferedImage gray) {
        int[] histogram = new int[256];
        int w = gray.getWidth(), h = gray.getHeight();
        Raster raster = gray.getRaster();

        // Build histogram
        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                histogram[raster.getSample(x, y, 0)]++;
            }
        }

        int total = w * h;
        float sum = 0;
        for (int i = 0; i < 256; i++) {
            sum += i * histogram[i];
        }

        float sumB = 0;
        int wB = 0;
        int wF;
        float maxVariance = 0;
        int threshold = 128;

        for (int i = 0; i < 256; i++) {
            wB += histogram[i];
            if (wB == 0) continue;

            wF = total - wB;
            if (wF == 0) break;

            sumB += i * histogram[i];
            float mB = sumB / wB;
            float mF = (sum - sumB) / wF;

            float varianceBetween = wB * wF * (mB - mF) * (mB - mF);

            if (varianceBetween > maxVariance) {
                maxVariance = varianceBetween;
                threshold = i;
            }
        }

        return threshold;
    }

    /**
     * Applies fixed-threshold binarization on a grayscale image.
     * Sets pixels below threshold to black (0), others to white (1).
     */
    private static BufferedImage binarize(BufferedImage gray, int threshold) {
        int w = gray.getWidth(), h = gray.getHeight();
        Raster in = gray.getRaster();
        BufferedImage binary = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_BINARY);
        WritableRaster out = binary.getRaster();

        // Process in batches for better cache locality
        int batchSize = 64;
        int[] line = new int[batchSize];

        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x += batchSize) {
                int currentBatch = Math.min(batchSize, w - x);
                in.getSamples(x, y, currentBatch, 1, 0, line);

                for (int i = 0; i < currentBatch; i++) {
                    line[i] = (line[i] < threshold) ? 0 : 1;
                }

                out.setSamples(x, y, currentBatch, 1, 0, line);
            }
        }

        return binary;
    }

    /**
     * Applies adaptive thresholding using parallel execution.
     * Each CPU core processes a chunk of image rows.
     */
    private static BufferedImage adaptiveBinarizeParallel(BufferedImage gray, int windowSize, int offset) {
        int w = gray.getWidth(), h = gray.getHeight();
        BufferedImage binary = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_BINARY);
        WritableRaster in = gray.getRaster();
        WritableRaster out = binary.getRaster();

        int cores = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(cores);
        int chunkSize = Math.max(1, h / cores);

        // Pre-calculate integral image for faster mean calculation
        int[][] integral = calculateIntegralImage(gray);

        CountDownLatch latch = new CountDownLatch(cores);
        for (int i = 0; i < cores; i++) {
            int startY = i * chunkSize;
            int endY = (i == cores - 1) ? h : startY + chunkSize;
            executor.submit(() -> {
                try {
                    adaptiveBinarizeChunk(in, out, w, h, startY, endY, windowSize, offset, integral);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            executor.shutdown();
        }

        return binary;
    }

    /**
     * Calculates an integral image for faster window operations.
     */
    private static int[][] calculateIntegralImage(BufferedImage image) {
        int w = image.getWidth();
        int h = image.getHeight();
        int[][] integral = new int[h + 1][w + 1];

        Raster raster = image.getRaster();

        // Fill integral image
        for (int y = 1; y <= h; y++) {
            int rowSum = 0;
            for (int x = 1; x <= w; x++) {
                rowSum += raster.getSample(x - 1, y - 1, 0);
                integral[y][x] = rowSum + integral[y - 1][x];
            }
        }

        return integral;
    }

    /**
     * Chunked adaptive thresholding logic using integral image for fast mean computation.
     */
    private static void adaptiveBinarizeChunk(Raster in, WritableRaster out, int w, int h,
                                              int yStart, int yEnd, int windowSize, int offset,
                                              int[][] integral) {
        int radius = windowSize / 2;

        for (int y = yStart; y < yEnd; y++) {
            for (int x = 0; x < w; x++) {
                // Calculate window bounds with edge handling
                int x1 = Math.max(0, x - radius);
                int y1 = Math.max(0, y - radius);
                int x2 = Math.min(w - 1, x + radius);
                int y2 = Math.min(h - 1, y + radius);

                // Calculate mean using integral image
                int count = (x2 - x1 + 1) * (y2 - y1 + 1);
                int sum = integral[y2 + 1][x2 + 1] - integral[y2 + 1][x1] -
                        integral[y1][x2 + 1] + integral[y1][x1];

                int localMean = sum / count;
                int pixel = in.getSample(x, y, 0);
                out.setSample(x, y, 0, pixel < (localMean - offset) ? 0 : 1);
            }
        }
    }

    /**
     * Detects skew angle by using a more efficient algorithm that samples the image
     * at key points instead of doing full rotations.
     */
    private static double detectSkewAngle(BufferedImage binary) {
        int w = binary.getWidth(), h = binary.getHeight();
        Raster raster = binary.getRaster();

        // For small images, use simpler Hough transform approach
        if (w * h < 1000000) {
            return detectSkewHough(binary);
        }

        double bestAngle = 0;
        double bestScore = Double.MIN_VALUE;

        // Use fewer angles for better performance
        for (double angle = -5; angle <= 5; angle += 0.5) {
            // Project image at this angle and measure variance
            double[] projection = projectAtAngle(raster, w, h, angle);
            double variance = computeVariance(projection);

            if (variance > bestScore) {
                bestScore = variance;
                bestAngle = angle;
            }
        }

        return bestAngle;
    }

    /**
     * Uses Hough transform to detect lines and determine skew angle.
     * More efficient for smaller images.
     */
    private static double detectSkewHough(BufferedImage binary) {
        int w = binary.getWidth(), h = binary.getHeight();
        Raster raster = binary.getRaster();

        // Sample the image for edge points
        int sampleStride = Math.max(1, Math.min(w, h) / 100);
        int maxCount = 0;
        double bestAngle = 0;

        // Simple angle histogram (10 degree buckets)
        int[] angleHistogram = new int[36];

        for (int y = sampleStride; y < h - sampleStride; y += sampleStride) {
            for (int x = sampleStride; x < w - sampleStride; x += sampleStride) {
                // Check if this is an edge point
                if (isEdgePoint(raster, x, y)) {
                    // Calculate gradient direction
                    double gradientAngle = calculateGradientAngle(raster, x, y);
                    int bucketIndex = (int)((gradientAngle + 180) / 10) % 36;
                    angleHistogram[bucketIndex]++;

                    if (angleHistogram[bucketIndex] > maxCount) {
                        maxCount = angleHistogram[bucketIndex];
                        bestAngle = (bucketIndex * 10) - 180;
                    }
                }
            }
        }

        // Convert the dominant angle to skew angle
        return (bestAngle + 90) % 180 - 90;
    }

    /**
     * Determines if a point is an edge point by checking neighbors.
     */
    private static boolean isEdgePoint(Raster raster, int x, int y) {
        int center = raster.getSample(x, y, 0);

        // Check if any neighboring pixel is different
        for (int dy = -1; dy <= 1; dy++) {
            for (int dx = -1; dx <= 1; dx++) {
                if (dx == 0 && dy == 0) continue;
                if (raster.getSample(x + dx, y + dy, 0) != center) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Calculates the gradient angle at a point using Sobel operator.
     */
    private static double calculateGradientAngle(Raster raster, int x, int y) {
        // Sobel operators
        int gx = (-1 * raster.getSample(x-1, y-1, 0)) + (-2 * raster.getSample(x-1, y, 0)) + (-1 * raster.getSample(x-1, y+1, 0)) +
                (1 * raster.getSample(x+1, y-1, 0)) + (2 * raster.getSample(x+1, y, 0)) + (1 * raster.getSample(x+1, y+1, 0));

        int gy = (-1 * raster.getSample(x-1, y-1, 0)) + (-2 * raster.getSample(x, y-1, 0)) + (-1 * raster.getSample(x+1, y-1, 0)) +
                (1 * raster.getSample(x-1, y+1, 0)) + (2 * raster.getSample(x, y+1, 0)) + (1 * raster.getSample(x+1, y+1, 0));

        return Math.toDegrees(Math.atan2(gy, gx));
    }

    /**
     * Projects the image at a specific angle and returns the projection.
     */
    private static double[] projectAtAngle(Raster raster, int w, int h, double angleDegrees) {
        double radians = Math.toRadians(angleDegrees);
        double sinTheta = Math.sin(radians);
        double cosTheta = Math.cos(radians);

        // Calculate projection size
        int diag = (int) Math.ceil(Math.sqrt(w * w + h * h));
        double[] projection = new double[diag * 2];

        // Sample the image
        int stride = Math.max(1, Math.min(w, h) / 100);
        for (int y = 0; y < h; y += stride) {
            for (int x = 0; x < w; x += stride) {
                if (raster.getSample(x, y, 0) == 0) { // Black pixel
                    // Project point
                    int projIndex = (int)(x * cosTheta + y * sinTheta + diag);
                    if (projIndex >= 0 && projIndex < projection.length) {
                        projection[projIndex]++;
                    }
                }
            }
        }

        return projection;
    }

    /**
     * Computes variance of an array (used to measure horizontal alignment).
     */
    private static double computeVariance(double[] values) {
        double sum = 0, sumSq = 0;

        for (double v : values) {
            sum += v;
            sumSq += v * v;
        }

        double mean = sum / values.length;
        return (sumSq / values.length) - (mean * mean);
    }

    /**
     * Rotates a binary image using Graphics2D with bilinear interpolation and
     * white background fill.
     */
    private static BufferedImage rotateImage(BufferedImage src, double angleDegrees) {
        // Skip rotation if angle is too small
        if (Math.abs(angleDegrees) < 0.05) {
            return src;
        }

        double radians = Math.toRadians(angleDegrees);
        int w = src.getWidth(), h = src.getHeight();
        double sin = Math.abs(Math.sin(radians)), cos = Math.abs(Math.cos(radians));
        int newW = (int) Math.floor(w * cos + h * sin);
        int newH = (int) Math.floor(h * cos + w * sin);

        BufferedImage rotated = new BufferedImage(newW, newH, BufferedImage.TYPE_BYTE_BINARY);
        Graphics2D g2d = rotated.createGraphics();

        // Use nearest neighbor for binary images (better for OCR)
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR);
        g2d.setBackground(Color.WHITE);
        g2d.clearRect(0, 0, newW, newH);
        g2d.translate(newW / 2.0, newH / 2.0);
        g2d.rotate(radians);
        g2d.translate(-w / 2.0, -h / 2.0);
        g2d.drawImage(src, 0, 0, null);
        g2d.dispose();

        return rotated;
    }

    /**
     * Encapsulates the image quality characteristics extracted during grading.
     */
    private static class Grade {
        int brightness;
        int contrast;
        boolean hasUnevenLighting;
        int noiseLevel;

        Grade(int brightness, int contrast, boolean uneven, int noise) {
            this.brightness = brightness;
            this.contrast = contrast;
            this.hasUnevenLighting = uneven;
            this.noiseLevel = noise;
        }
    }
}
