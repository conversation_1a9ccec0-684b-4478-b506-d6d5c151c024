package com.vitality.ai.ocr.service;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.Loader;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Service
public class PdfImageExtractor {

    /**
     * Extract images from a PDF file
     * @param pdfFile The PDF file
     * @param dpi The resolution to render at (e.g., 300)
     * @return List of base64 encoded images with data URI format
     */
    public List<String> extractImagesFromPdf(File pdfFile, int dpi) throws IOException {
        List<String> images = new ArrayList<>();

        // Load the PDF document
        try (PDDocument document = Loader.loadPDF(pdfFile)) {
            PDFRenderer renderer = new PDFRenderer(document);

            // Process each page
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                // Render the page as an image
                // Higher DPI = higher quality but larger size
                float scale = dpi / 72f; // Convert DPI to scale (PDF uses 72 DPI by default)
                BufferedImage image = renderer.renderImageWithDPI(pageIndex, dpi);

                // Convert the image to PNG format and get bytes
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(image, "png", baos);
                byte[] imageBytes = baos.toByteArray();

                // Encode as base64 with proper data URI format
                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                String dataUri = "data:image/png;base64," + base64Image;

                images.add(dataUri);
            }
        }

        return images;
    }

    /**
     * Extract a specific page as an image
     * @param pdfFile The PDF file
     * @param pageIndex The 0-based page index to extract
     * @param dpi The resolution to render at
     * @return Base64 encoded image with data URI format
     */
    public String extractPageAsImage(File pdfFile, int pageIndex, int dpi) throws IOException {
        // Load the PDF document
        try (PDDocument document = Loader.loadPDF(pdfFile)) {
            if (pageIndex < 0 || pageIndex >= document.getNumberOfPages()) {
                throw new IllegalArgumentException("Page index out of range: " + pageIndex);
            }

            // Render the specified page as an image
            PDFRenderer renderer = new PDFRenderer(document);
            BufferedImage image = renderer.renderImageWithDPI(pageIndex, dpi);

            // Convert to PNG
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            byte[] imageBytes = baos.toByteArray();
//            if (false) {
                imageBytes = SmartOCRImageProcessor.process(pageIndex, imageBytes);
//            }

            // Encode as base64 with proper data URI format
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            return base64Image;
        }
    }

    /**
     * Get the total number of pages in a PDF
     */
    public int getPageCount(File pdfFile) throws IOException {
        try (PDDocument document = Loader.loadPDF(pdfFile)) {
            return document.getNumberOfPages();
        }
    }
}
