package com.vitality.ai.ocr.example;

import com.vitality.ai.ocr.service.PdfToScannedPdfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Example class demonstrating how to use the PdfToScannedPdfService.
 * This component runs only when the property 'pdf.scanner.example.enabled' is set to true.
 * 
 * Usage examples:
 * 1. Convert from file to file
 * 2. Convert from InputStream to byte array
 * 3. Convert with custom DPI settings
 * 4. Handle different input/output scenarios
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "pdf.scanner.example.enabled", havingValue = "true")
public class PdfToScannedPdfExample implements CommandLineRunner {

    private final PdfToScannedPdfService pdfToScannedPdfService;

    public PdfToScannedPdfExample(PdfToScannedPdfService pdfToScannedPdfService) {
        this.pdfToScannedPdfService = pdfToScannedPdfService;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("Running PDF to Scanned PDF conversion examples...");
        
        // Example 1: Convert from file to file
        exampleFileToFile();
        
        // Example 2: Convert from InputStream
        exampleInputStreamConversion();
        
        // Example 3: Convert with custom DPI
        exampleCustomDpiConversion();
        
        // Example 4: Convert to OutputStream
        exampleOutputStreamConversion();
        
        // Example 5: Convert byte array
        exampleByteArrayConversion();
        
        log.info("All PDF conversion examples completed successfully!");
    }

    /**
     * Example 1: Convert a PDF file to a scanned PDF file using default settings.
     */
    private void exampleFileToFile() {
        try {
            log.info("=== Example 1: File to File Conversion ===");
            
            // For this example, you would need to provide an actual PDF file
            // String inputPath = "/path/to/your/input.pdf";
            // String outputPath = "/path/to/your/output_scanned.pdf";
            
            // File inputFile = new File(inputPath);
            // if (!inputFile.exists()) {
            //     log.warn("Input file not found: {}. Skipping file-to-file example.", inputPath);
            //     return;
            // }
            
            // Convert the PDF
            // byte[] scannedPdfBytes = pdfToScannedPdfService.convertToScannedPdf(inputFile);
            
            // Save the result
            // Files.write(Paths.get(outputPath), scannedPdfBytes);
            
            // log.info("Successfully converted {} to scanned PDF: {}", inputPath, outputPath);
            // log.info("Original file size: {} bytes", inputFile.length());
            // log.info("Scanned PDF size: {} bytes", scannedPdfBytes.length);
            
            log.info("File-to-file example skipped (no input file provided)");
            
        } catch (Exception e) {
            log.error("Error in file-to-file conversion example", e);
        }
    }

    /**
     * Example 2: Convert from InputStream to byte array.
     */
    private void exampleInputStreamConversion() {
        try {
            log.info("=== Example 2: InputStream Conversion ===");
            
            // This example shows how to convert from an InputStream
            // Useful when receiving PDF data from web requests, databases, etc.
            
            // Example with a hypothetical InputStream
            // try (InputStream inputStream = getInputStreamFromSomewhere()) {
            //     byte[] scannedPdfBytes = pdfToScannedPdfService.convertToScannedPdf(inputStream);
            //     
            //     log.info("Successfully converted PDF from InputStream");
            //     log.info("Scanned PDF size: {} bytes", scannedPdfBytes.length);
            //     
            //     // You can now save it, return it from a web service, etc.
            //     // Files.write(Paths.get("output_from_stream.pdf"), scannedPdfBytes);
            // }
            
            log.info("InputStream conversion example completed (demonstration only)");
            
        } catch (Exception e) {
            log.error("Error in InputStream conversion example", e);
        }
    }

    /**
     * Example 3: Convert with custom DPI settings.
     */
    private void exampleCustomDpiConversion() {
        try {
            log.info("=== Example 3: Custom DPI Conversion ===");
            
            // Different DPI settings for different use cases:
            // - 150 DPI: Minimum readable quality, smaller file size
            // - 300 DPI: Standard quality, good balance of quality and size
            // - 600 DPI: High quality, larger file size
            
            int[] dpiOptions = {150, 300, 600};
            
            for (int dpi : dpiOptions) {
                log.info("DPI {} - Recommended for: {}", dpi, getDpiRecommendation(dpi));
                
                // Example conversion with custom DPI
                // File inputFile = new File("input.pdf");
                // if (inputFile.exists()) {
                //     byte[] scannedPdf = pdfToScannedPdfService.convertToScannedPdf(inputFile, dpi);
                //     String outputPath = String.format("output_%ddpi.pdf", dpi);
                //     Files.write(Paths.get(outputPath), scannedPdf);
                //     log.info("Created {} with {} DPI (size: {} bytes)", outputPath, dpi, scannedPdf.length);
                // }
            }
            
            log.info("Custom DPI conversion examples completed");
            
        } catch (Exception e) {
            log.error("Error in custom DPI conversion example", e);
        }
    }

    /**
     * Example 4: Convert to OutputStream (useful for web responses).
     */
    private void exampleOutputStreamConversion() {
        try {
            log.info("=== Example 4: OutputStream Conversion ===");
            
            // This is useful for web services where you want to stream the result directly
            // to the HTTP response without creating intermediate byte arrays
            
            // Example with ByteArrayOutputStream (could be HttpServletResponse.getOutputStream())
            // try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            //     File inputFile = new File("input.pdf");
            //     if (inputFile.exists()) {
            //         pdfToScannedPdfService.convertToScannedPdf(inputFile, outputStream, 300);
            //         
            //         byte[] result = outputStream.toByteArray();
            //         log.info("Successfully wrote scanned PDF to OutputStream");
            //         log.info("Output size: {} bytes", result.length);
            //     }
            // }
            
            log.info("OutputStream conversion example completed (demonstration only)");
            
        } catch (Exception e) {
            log.error("Error in OutputStream conversion example", e);
        }
    }

    /**
     * Example 5: Convert from byte array (useful for in-memory processing).
     */
    private void exampleByteArrayConversion() {
        try {
            log.info("=== Example 5: Byte Array Conversion ===");
            
            // This is useful when you already have PDF data in memory
            // For example, from a database BLOB, web service response, etc.
            
            // Example:
            // byte[] inputPdfBytes = getPdfBytesFromDatabase();
            // byte[] scannedPdfBytes = pdfToScannedPdfService.convertToScannedPdf(inputPdfBytes);
            // 
            // log.info("Successfully converted PDF from byte array");
            // log.info("Input size: {} bytes", inputPdfBytes.length);
            // log.info("Output size: {} bytes", scannedPdfBytes.length);
            
            log.info("Byte array conversion example completed (demonstration only)");
            
        } catch (Exception e) {
            log.error("Error in byte array conversion example", e);
        }
    }

    /**
     * Get recommendation text for different DPI settings.
     */
    private String getDpiRecommendation(int dpi) {
        return switch (dpi) {
            case 150 -> "Basic quality, smaller files, fast processing";
            case 300 -> "Standard quality, balanced size/quality, recommended for most use cases";
            case 600 -> "High quality, larger files, detailed documents";
            default -> "Custom setting";
        };
    }

    /**
     * Example method showing how you might integrate this service into a web controller.
     */
    public void webServiceExample() {
        // This is a conceptual example of how you might use the service in a web controller
        
        /*
        @PostMapping("/convert-to-scanned")
        public ResponseEntity<byte[]> convertToScannedPdf(
                @RequestParam("file") MultipartFile file,
                @RequestParam(value = "dpi", defaultValue = "300") int dpi) {
            
            try {
                // Convert MultipartFile to byte array
                byte[] inputBytes = file.getBytes();
                
                // Convert to scanned PDF
                byte[] scannedPdfBytes = pdfToScannedPdfService.convertToScannedPdf(inputBytes, dpi);
                
                // Return as downloadable file
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_PDF);
                headers.setContentDispositionFormData("attachment", "scanned_" + file.getOriginalFilename());
                
                return ResponseEntity.ok()
                        .headers(headers)
                        .body(scannedPdfBytes);
                        
            } catch (IOException e) {
                log.error("Error converting PDF to scanned format", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }
        */
    }

    /**
     * Example method showing batch processing of multiple PDFs.
     */
    public void batchProcessingExample() {
        // This shows how you might process multiple PDFs in batch
        
        /*
        public void processPdfDirectory(String inputDir, String outputDir, int dpi) {
            try {
                Path inputPath = Paths.get(inputDir);
                Path outputPath = Paths.get(outputDir);
                
                // Create output directory if it doesn't exist
                Files.createDirectories(outputPath);
                
                // Process all PDF files in the input directory
                Files.walk(inputPath)
                        .filter(path -> path.toString().toLowerCase().endsWith(".pdf"))
                        .forEach(pdfPath -> {
                            try {
                                File inputFile = pdfPath.toFile();
                                byte[] scannedPdf = pdfToScannedPdfService.convertToScannedPdf(inputFile, dpi);
                                
                                String outputFileName = "scanned_" + inputFile.getName();
                                Path outputFilePath = outputPath.resolve(outputFileName);
                                Files.write(outputFilePath, scannedPdf);
                                
                                log.info("Processed: {} -> {}", inputFile.getName(), outputFileName);
                                
                            } catch (IOException e) {
                                log.error("Error processing file: {}", pdfPath, e);
                            }
                        });
                        
            } catch (IOException e) {
                log.error("Error in batch processing", e);
            }
        }
        */
    }
}
