#!/bin/bash

# Simple script to run the PDF test without Spring Boot

echo "🚀 Running PDF to Scanned PDF Service Test..."

# Set the classpath
CLASSPATH="build/classes/java/main"

# Add PDFBox and dependencies to classpath
CLASSPATH="$CLASSPATH:$HOME/.gradle/caches/modules-2/files-2.1/org.apache.pdfbox/pdfbox/3.0.3/*/pdfbox-3.0.3.jar"
CLASSPATH="$CLASSPATH:$HOME/.gradle/caches/modules-2/files-2.1/org.apache.pdfbox/fontbox/3.0.3/*/fontbox-3.0.3.jar"
CLASSPATH="$CLASSPATH:$HOME/.gradle/caches/modules-2/files-2.1/commons-logging/commons-logging/1.3.4/*/commons-logging-1.3.4.jar"

# Add SLF4J for logging
CLASSPATH="$CLASSPATH:$HOME/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-api/2.0.17/*/slf4j-api-2.0.17.jar"
CLASSPATH="$CLASSPATH:$HOME/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-simple/2.0.17/*/slf4j-simple-2.0.17.jar"

echo "📚 Classpath configured"

# Run the test
java -cp "$CLASSPATH" com.vitality.ai.ocr.demo.SimplePdfTest

echo "✅ Test completed!"
